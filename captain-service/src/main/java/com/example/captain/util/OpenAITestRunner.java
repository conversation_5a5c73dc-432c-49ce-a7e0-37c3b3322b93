package com.example.captain.util;

import com.example.captain.model.ProductDimensionsAndWeight;
import com.example.captain.service.OpenAIProductAnalysisService;
import com.fasterxml.jackson.databind.ObjectMapper;

/**
 * OpenAI测试运行器
 * 用于快速测试OpenAI Function Call功能
 *
 * 使用方法：
 * 1. 在application.yml中配置您的OpenAI API密钥
 * 2. 运行此类的main方法
 */
public class OpenAITestRunner {

    public static void main(String[] args) {
        System.out.println("🚀 OpenAI商品信息提取测试");
        System.out.println("============================================================");

        // 初始化服务
        ObjectMapper objectMapper = new ObjectMapper();
        // 注意：请在application.yml中配置真实的API密钥
        String apiKey = "********************************************************************************************************************************************************************"; // 请替换为真实的API密钥
        String model = "gpt-3.5-turbo";
        OpenAIProductAnalysisService service = new OpenAIProductAnalysisService(apiKey, objectMapper, model);

        // 测试案例1: 蓝牙耳机
        testBluetoothHeadphones(service);

        // 测试案例2: 笔记本支架
        testLaptopStand(service);

        // 测试案例3: 无线充电器
        testWirelessCharger(service);

        System.out.println("✅ 所有测试完成！");
    }

    private static void testBluetoothHeadphones(OpenAIProductAnalysisService service) {
        System.out.println("\n🎧 测试案例1: 蓝牙耳机");
        System.out.println("----------------------------------------");

        String title = "Wireless Bluetooth Headphones - Over Ear Headphones with Microphone, 40H Playtime, Deep Bass, Weight: 0.5kg";
        String feature = "【40H Playtime】: 40 hours wireless mode. Weight: 500g. Dimensions: 20cm x 15cm x 8cm. 【Premium Sound】: Advanced 40mm drivers.";
        String description = "Premium wireless Bluetooth headphones. Package dimensions: 20 x 15 x 8 centimeters. Shipping weight: 500 grams. Comfortable over-ear design.";

        runTest(service, title, feature, description);
    }

    private static void testLaptopStand(OpenAIProductAnalysisService service) {
        System.out.println("\n💻 测试案例2: 笔记本支架");
        System.out.println("----------------------------------------");

        String title = "Adjustable Laptop Stand for Desk, Aluminum Laptop Riser, Ergonomic Computer Stand";
        String feature = "【Ergonomic Design】: 6 adjustable levels. Weight: 1.2kg. Size: 28cm(L) x 22cm(W) x 6cm(H). 【Sturdy】: Premium aluminum alloy.";
        String description = "High-quality aluminum laptop stand. Product dimensions: 28 x 22 x 6 cm. Net weight: 1200g. Supports 10-17.3 inch laptops.";

        runTest(service, title, feature, description);
    }

    private static void testWirelessCharger(OpenAIProductAnalysisService service) {
        System.out.println("\n🔋 测试案例3: 无线充电器");
        System.out.println("----------------------------------------");

        String title = "Wireless Charger, 15W Fast Wireless Charging Pad Compatible with iPhone, Samsung";
        String feature = "【15W Fast Charging】: Up to 15W fast charging. 【Compact】: 8mm thick. Dimensions: 10cm diameter x 0.8cm height. Weight: 150g.";
        String description = "Compact wireless charging pad. Specifications: Diameter 10cm, Height 0.8cm, Weight 150g. LED indicator included.";

        runTest(service, title, feature, description);
    }

    private static void runTest(OpenAIProductAnalysisService service, String title, String feature, String description) {
        // 打印输入
        System.out.println("📝 输入:");
        System.out.println("Title: " + truncate(title, 80));
        System.out.println("Feature: " + truncate(feature, 80));
        System.out.println("Description: " + truncate(description, 80));

        System.out.println("\n🔄 调用OpenAI API...");

        try {
            // 执行提取
            ProductDimensionsAndWeight result = service.extractDimensionsAndWeight(title, feature, description);

            // 打印结果
            System.out.println("\n📊 提取结果:");
            if (result != null && result.hasValidData()) {
                printResult(result);
                System.out.println("✅ 提取成功");
            } else {
                System.out.println("❌ 未提取到有效数据");
            }
        } catch (Exception e) {
            System.out.println("❌ 提取失败: " + e.getMessage());
        }

        System.out.println();
    }

    private static void printResult(ProductDimensionsAndWeight result) {
        // 重量
        if (result.getShippingWeight() != null) {
            System.out.println("🏋️ 重量: " + result.getShippingWeight() + " " +
                (result.getShippingWeightUnitOfMeasure() != null ? result.getShippingWeightUnitOfMeasure() : ""));
        }

        // 尺寸
        boolean hasDimensions = false;
        StringBuilder dimensions = new StringBuilder("📏 尺寸: ");

        if (result.getItemDepthFrontToBack() != null) {
            dimensions.append("长").append(result.getItemDepthFrontToBack());
            if (result.getItemDepthUnit() != null) dimensions.append(result.getItemDepthUnit());
            hasDimensions = true;
        }

        if (result.getItemWidthSideToSide() != null) {
            if (hasDimensions) dimensions.append(" × ");
            dimensions.append("宽").append(result.getItemWidthSideToSide());
            if (result.getItemWidthUnit() != null) dimensions.append(result.getItemWidthUnit());
            hasDimensions = true;
        }

        if (result.getItemHeightFloorToTop() != null) {
            if (hasDimensions) dimensions.append(" × ");
            dimensions.append("高").append(result.getItemHeightFloorToTop());
            if (result.getItemHeightUnitOfMeasure() != null) dimensions.append(result.getItemHeightUnitOfMeasure());
            hasDimensions = true;
        }

        if (hasDimensions) {
            System.out.println(dimensions.toString());
        }
    }

    private static String truncate(String text, int maxLength) {
        if (text == null) return "null";
        if (text.length() <= maxLength) return text;
        return text.substring(0, maxLength - 3) + "...";
    }
}
