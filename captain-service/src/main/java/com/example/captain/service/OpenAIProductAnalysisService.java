package com.example.captain.service;

import com.example.captain.model.ProductDimensionsAndWeight;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.openai.client.OpenAIClient;
import com.openai.client.okhttp.OpenAIOkHttpClient;
import com.openai.models.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.net.InetSocketAddress;
import java.net.Proxy;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * OpenAI商品分析服务
 * 使用OpenAI Function Call从商品信息中提取重量和尺寸数据
 */
@Service
@Slf4j
public class OpenAIProductAnalysisService {

    private final OpenAIClient openAIClient;
    private final ObjectMapper objectMapper;

    @Value("${openai.model:gpt-3.5-turbo}")
    private String model;

    public OpenAIProductAnalysisService(@Value("${openai.api-key:}") String apiKey,
                                       ObjectMapper objectMapper) {
        this(apiKey, objectMapper, "gpt-3.5-turbo");
    }

    /**
     * 构造函数，支持手动设置model参数（用于测试）
     */
    public OpenAIProductAnalysisService(String apiKey, ObjectMapper objectMapper, String model) {
        this.objectMapper = objectMapper;
        this.model = model;

        if (!StringUtils.hasText(apiKey)) {
            log.warn("OpenAI API密钥未配置，OpenAI功能将不可用");
            this.openAIClient = null;
        } else {
            this.openAIClient = createOpenAIClientWithProxy(apiKey);
            log.info("OpenAI服务已初始化，模型: {}", model);
        }
    }

    /**
     * 创建支持代理的OpenAI客户端
     */
    private OpenAIClient createOpenAIClientWithProxy(String apiKey) {
        // 检查系统代理设置
        String proxyHost = System.getProperty("http.proxyHost");
        String proxyPort = System.getProperty("http.proxyPort");
        String socksProxyHost = System.getProperty("socksProxyHost");
        String socksProxyPort = System.getProperty("socksProxyPort");

        if (StringUtils.hasText(proxyHost) && StringUtils.hasText(proxyPort)) {
            log.info("检测到HTTP代理配置: {}:{}", proxyHost, proxyPort);
        } else if (StringUtils.hasText(socksProxyHost) && StringUtils.hasText(socksProxyPort)) {
            log.info("检测到SOCKS代理配置: {}:{}", socksProxyHost, socksProxyPort);
        } else {
            log.info("未检测到系统代理配置，将尝试使用Clash默认配置");
            // 设置Clash默认代理
            System.setProperty("http.proxyHost", "127.0.0.1");
            System.setProperty("http.proxyPort", "7890");
            System.setProperty("https.proxyHost", "127.0.0.1");
            System.setProperty("https.proxyPort", "7890");
            log.info("已设置Clash默认HTTP代理: 127.0.0.1:7890");
        }

        return OpenAIOkHttpClient.builder()
                .apiKey(apiKey)
                .build();
    }

    /**
     * 从商品信息中提取重量和尺寸数据
     *
     * @param title 商品标题
     * @param feature 商品特性
     * @param description 商品描述
     * @return 提取的重量和尺寸信息，如果提取失败返回null
     */
    public ProductDimensionsAndWeight extractDimensionsAndWeight(String title, String feature, String description) {
        if (openAIClient == null) {
            log.warn("OpenAI服务未初始化，跳过重量和尺寸提取");
            return null;
        }

        try {
            log.info("开始使用OpenAI提取商品重量和尺寸信息");

            // 构建提示词
            String prompt = buildPrompt(title, feature, description);

            // 构建Function定义
            FunctionDefinition functionDefinition = createFunctionDefinition();

            // 构建请求
            ChatCompletionCreateParams request = ChatCompletionCreateParams.builder()
                    .model(model)
                    .messages(Arrays.asList(
                            ChatCompletionMessageParam.ofChatCompletionUserMessageParam(
                                    ChatCompletionUserMessageParam.builder()
                                            .content(ChatCompletionUserMessageParam.Content.ofTextContent(prompt))
                                            .role(ChatCompletionUserMessageParam.Role.USER)
                                            .build()
                            )
                    ))
                    .tools(Arrays.asList(
                            ChatCompletionTool.builder()
                                    .type(ChatCompletionTool.Type.FUNCTION)
                                    .function(functionDefinition)
                                    .build()
                    ))
                    .toolChoice(ChatCompletionToolChoiceOption.ofChatCompletionNamedToolChoice(
                            ChatCompletionNamedToolChoice.builder()
                                    .type(ChatCompletionNamedToolChoice.Type.FUNCTION)
                                    .function(ChatCompletionNamedToolChoice.Function.builder()
                                            .name("extract_product_dimensions_weight")
                                            .build())
                                    .build()
                    ))
                    .maxTokens(500)
                    .temperature(0.1)
                    .build();

            // 调用OpenAI API
            ChatCompletion response = openAIClient.chat().completions().create(request);

            // 处理响应
            return processResponse(response);

        } catch (Exception e) {
            log.error("使用OpenAI提取商品重量和尺寸时出错: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 构建提示词
     */
    private String buildPrompt(String title, String feature, String description) {
        StringBuilder prompt = new StringBuilder();
        prompt.append("请从以下商品信息中提取运输重量和包装尺寸信息：\n\n");

        if (StringUtils.hasText(title)) {
            prompt.append("商品标题：").append(title).append("\n\n");
        }

        if (StringUtils.hasText(feature)) {
            prompt.append("商品特性：").append(feature).append("\n\n");
        }

        if (StringUtils.hasText(description)) {
            prompt.append("商品描述：").append(description).append("\n\n");
        }

        prompt.append("严格要求：\n");
        prompt.append("1. 运输重量：必须转换为KG单位输出\n");
        prompt.append("   - 如果是克(g/gram/grams)，除以1000转换为KG\n");
        prompt.append("   - 如果是磅(lb/pound)，乘以0.453592转换为KG\n");
        prompt.append("   - 输出格式：数字 + \"KG\"\n");
        prompt.append("2. 包装尺寸：必须转换为CM单位输出\n");
        prompt.append("   - 如果是毫米(mm)，除以10转换为CM\n");
        prompt.append("   - 如果是米(m)，乘以100转换为CM\n");
        prompt.append("   - 如果是英寸(inch/in)，乘以2.54转换为CM\n");
        prompt.append("   - 输出格式：数字 + \"CM\"\n");
        prompt.append("3. 单位输出要求：\n");
        prompt.append("   - shippingWeightUnitOfMeasure 必须是 \"KG\"\n");
        prompt.append("   - itemDepthUnit 必须是 \"CM\"\n");
        prompt.append("   - itemWidthUnit 必须是 \"CM\"\n");
        prompt.append("   - itemHeightUnitOfMeasure 必须是 \"CM\"\n");
        prompt.append("4. 如果信息不明确或缺失，对应字段设为null\n");
        prompt.append("5. 重量和尺寸必须是正数\n");
        prompt.append("6. 禁止输出原始单位，必须转换后输出");

        return prompt.toString();
    }

    /**
     * 创建Function定义
     */
    private FunctionDefinition createFunctionDefinition() {
        // 构建JSON Schema
        Map<String, Object> schema = new HashMap<>();
        schema.put("type", "object");

        Map<String, Object> properties = new HashMap<>();

        // 运输重量
        Map<String, Object> shippingWeight = new HashMap<>();
        shippingWeight.put("type", "number");
        shippingWeight.put("description", "运输重量数值，必须转换为KG单位。如果原始单位是g，需要除以1000；如果是lb，需要乘以0.453592");
        properties.put("shippingWeight", shippingWeight);

        Map<String, Object> shippingWeightUnit = new HashMap<>();
        shippingWeightUnit.put("type", "string");
        shippingWeightUnit.put("description", "运输重量单位，必须固定为\"KG\"");
        shippingWeightUnit.put("enum", Arrays.asList("KG"));
        properties.put("shippingWeightUnitOfMeasure", shippingWeightUnit);

        // 深度（长度）
        Map<String, Object> itemDepth = new HashMap<>();
        itemDepth.put("type", "number");
        itemDepth.put("description", "商品深度（前后）数值，必须转换为CM单位。如果原始单位是mm，需要除以10；如果是m，需要乘以100；如果是inch，需要乘以2.54");
        properties.put("itemDepthFrontToBack", itemDepth);

        Map<String, Object> itemDepthUnit = new HashMap<>();
        itemDepthUnit.put("type", "string");
        itemDepthUnit.put("description", "深度单位，必须固定为\"CM\"");
        itemDepthUnit.put("enum", Arrays.asList("CM"));
        properties.put("itemDepthUnit", itemDepthUnit);

        // 宽度
        Map<String, Object> itemWidth = new HashMap<>();
        itemWidth.put("type", "number");
        itemWidth.put("description", "商品宽度（左右）数值，必须转换为CM单位。如果原始单位是mm，需要除以10；如果是m，需要乘以100；如果是inch，需要乘以2.54");
        properties.put("itemWidthSideToSide", itemWidth);

        Map<String, Object> itemWidthUnit = new HashMap<>();
        itemWidthUnit.put("type", "string");
        itemWidthUnit.put("description", "宽度单位，必须固定为\"CM\"");
        itemWidthUnit.put("enum", Arrays.asList("CM"));
        properties.put("itemWidthUnit", itemWidthUnit);

        // 高度
        Map<String, Object> itemHeight = new HashMap<>();
        itemHeight.put("type", "number");
        itemHeight.put("description", "商品高度（底部到顶部）数值，必须转换为CM单位。如果原始单位是mm，需要除以10；如果是m，需要乘以100；如果是inch，需要乘以2.54");
        properties.put("itemHeightFloorToTop", itemHeight);

        Map<String, Object> itemHeightUnit = new HashMap<>();
        itemHeightUnit.put("type", "string");
        itemHeightUnit.put("description", "高度单位，必须固定为\"CM\"");
        itemHeightUnit.put("enum", Arrays.asList("CM"));
        properties.put("itemHeightUnitOfMeasure", itemHeightUnit);

        schema.put("properties", properties);

        try {
            String schemaJson = objectMapper.writeValueAsString(schema);
            return FunctionDefinition.builder()
                    .name("extract_product_dimensions_weight")
                    .description("从商品信息中提取运输重量（KG）和包装尺寸（CM）")
                    .parameters(FunctionParameters.builder()
                            .putAdditionalProperty("type", com.openai.core.JsonValue.from("object"))
                            .putAdditionalProperty("properties", com.openai.core.JsonValue.from(properties))
                            .build())
                    .build();
        } catch (JsonProcessingException e) {
            log.error("创建Function定义时出错: {}", e.getMessage(), e);
            throw new RuntimeException("创建Function定义失败", e);
        }
    }

    /**
     * 处理OpenAI响应
     */
    private ProductDimensionsAndWeight processResponse(ChatCompletion response) {
        try {
            List<ChatCompletion.Choice> choices = response.choices();
            if (choices == null || choices.isEmpty()) {
                log.warn("OpenAI响应为空");
                return null;
            }

            ChatCompletion.Choice choice = choices.get(0);
            ChatCompletionMessage message = choice.message();

            List<ChatCompletionMessageToolCall> toolCalls = message.toolCalls().orElse(null);
            if (toolCalls == null || toolCalls.isEmpty()) {
                log.warn("OpenAI响应中没有tool calls");
                return null;
            }

            ChatCompletionMessageToolCall toolCall = toolCalls.get(0);
            if (toolCall.function() == null) {
                log.warn("OpenAI响应中没有function call");
                return null;
            }

            String argumentsJson = toolCall.function().arguments();
            log.info("OpenAI返回的参数: {}", argumentsJson);

            // 解析JSON参数
            JsonNode argumentsNode = objectMapper.readTree(argumentsJson);

            ProductDimensionsAndWeight result = ProductDimensionsAndWeight.builder()
                    .shippingWeight(parseDecimal(argumentsNode, "shippingWeight"))
                    .shippingWeightUnitOfMeasure(parseString(argumentsNode, "shippingWeightUnitOfMeasure"))
                    .itemDepthFrontToBack(parseDecimal(argumentsNode, "itemDepthFrontToBack"))
                    .itemDepthUnit(parseString(argumentsNode, "itemDepthUnit"))
                    .itemWidthSideToSide(parseDecimal(argumentsNode, "itemWidthSideToSide"))
                    .itemWidthUnit(parseString(argumentsNode, "itemWidthUnit"))
                    .itemHeightFloorToTop(parseDecimal(argumentsNode, "itemHeightFloorToTop"))
                    .itemHeightUnitOfMeasure(parseString(argumentsNode, "itemHeightUnitOfMeasure"))
                    .build();

            log.info("成功提取商品重量和尺寸信息: {}", result);
            return result;

        } catch (JsonProcessingException e) {
            log.error("解析OpenAI响应JSON时出错: {}", e.getMessage(), e);
            return null;
        } catch (Exception e) {
            log.error("处理OpenAI响应时出错: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 从JSON节点中解析BigDecimal值
     */
    private BigDecimal parseDecimal(JsonNode node, String fieldName) {
        JsonNode fieldNode = node.get(fieldName);
        if (fieldNode == null || fieldNode.isNull()) {
            return null;
        }

        try {
            return new BigDecimal(fieldNode.asText());
        } catch (NumberFormatException e) {
            log.warn("无法解析字段 {} 的数值: {}", fieldName, fieldNode.asText());
            return null;
        }
    }

    /**
     * 从JSON节点中解析字符串值
     */
    private String parseString(JsonNode node, String fieldName) {
        JsonNode fieldNode = node.get(fieldName);
        if (fieldNode == null || fieldNode.isNull()) {
            return null;
        }

        String value = fieldNode.asText();
        return StringUtils.hasText(value) ? value : null;
    }
}
