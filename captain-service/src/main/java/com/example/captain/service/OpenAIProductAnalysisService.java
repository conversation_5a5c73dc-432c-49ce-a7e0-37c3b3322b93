package com.example.captain.service;

import com.example.captain.model.ProductDimensionsAndWeight;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.openai.client.OpenAIClient;
import com.openai.client.okhttp.OpenAIOkHttpClient;
import com.openai.models.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.net.InetSocketAddress;
import java.net.Proxy;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * OpenAI商品分析服务
 * 使用OpenAI Function Call从商品信息中提取重量和尺寸数据
 */
@Service
@Slf4j
public class OpenAIProductAnalysisService {

    private final OpenAIClient openAIClient;
    private final ObjectMapper objectMapper;

    @Value("${openai.model:gpt-4o}")
    private String model;

    public OpenAIProductAnalysisService(@Value("${openai.api-key:}") String apiKey,
                                       ObjectMapper objectMapper) {
        this(apiKey, objectMapper, "gpt-4o");
    }

    /**
     * 构造函数，支持手动设置model参数（用于测试）
     */
    public OpenAIProductAnalysisService(String apiKey, ObjectMapper objectMapper, String model) {
        this.objectMapper = objectMapper;
        this.model = model;

        if (!StringUtils.hasText(apiKey)) {
            log.warn("OpenAI API密钥未配置，OpenAI功能将不可用");
            this.openAIClient = null;
        } else {
            this.openAIClient = createOpenAIClientWithProxy(apiKey);
            log.info("OpenAI服务已初始化，模型: {}", model);
        }
    }

    /**
     * 创建支持代理的OpenAI客户端
     */
    private OpenAIClient createOpenAIClientWithProxy(String apiKey) {
        // 检查系统代理设置
        String proxyHost = System.getProperty("http.proxyHost");
        String proxyPort = System.getProperty("http.proxyPort");
        String socksProxyHost = System.getProperty("socksProxyHost");
        String socksProxyPort = System.getProperty("socksProxyPort");

        if (StringUtils.hasText(proxyHost) && StringUtils.hasText(proxyPort)) {
            log.info("检测到HTTP代理配置: {}:{}", proxyHost, proxyPort);
        } else if (StringUtils.hasText(socksProxyHost) && StringUtils.hasText(socksProxyPort)) {
            log.info("检测到SOCKS代理配置: {}:{}", socksProxyHost, socksProxyPort);
        } else {
            log.info("未检测到系统代理配置，将尝试使用Clash默认配置");
            // 设置Clash默认代理
            System.setProperty("http.proxyHost", "127.0.0.1");
            System.setProperty("http.proxyPort", "7890");
            System.setProperty("https.proxyHost", "127.0.0.1");
            System.setProperty("https.proxyPort", "7890");
            log.info("已设置Clash默认HTTP代理: 127.0.0.1:7890");
        }

        return OpenAIOkHttpClient.builder()
                .apiKey(apiKey)
                .build();
    }

    /**
     * 从商品信息中提取重量和尺寸数据
     *
     * @param title 商品标题
     * @param feature 商品特性
     * @param description 商品描述
     * @return 提取的重量和尺寸信息，如果提取失败返回null
     */
    public ProductDimensionsAndWeight extractDimensionsAndWeight(String title, String feature, String description) {
        if (openAIClient == null) {
            log.warn("OpenAI服务未初始化，跳过重量和尺寸提取");
            return null;
        }

        try {
            log.info("开始使用OpenAI提取商品重量和尺寸信息");

            // 构建提示词
            String prompt = buildPrompt(title, feature, description);

            // 构建Function定义
            FunctionDefinition functionDefinition = createFunctionDefinition();

            // 构建请求
            ChatCompletionCreateParams request = ChatCompletionCreateParams.builder()
                    .model(model)
                    .messages(Arrays.asList(
                            ChatCompletionMessageParam.ofChatCompletionUserMessageParam(
                                    ChatCompletionUserMessageParam.builder()
                                            .content(ChatCompletionUserMessageParam.Content.ofTextContent(prompt))
                                            .role(ChatCompletionUserMessageParam.Role.USER)
                                            .build()
                            )
                    ))
                    .tools(Arrays.asList(
                            ChatCompletionTool.builder()
                                    .type(ChatCompletionTool.Type.FUNCTION)
                                    .function(functionDefinition)
                                    .build()
                    ))
                    .toolChoice(ChatCompletionToolChoiceOption.ofChatCompletionNamedToolChoice(
                            ChatCompletionNamedToolChoice.builder()
                                    .type(ChatCompletionNamedToolChoice.Type.FUNCTION)
                                    .function(ChatCompletionNamedToolChoice.Function.builder()
                                            .name("extract_product_dimensions_weight")
                                            .build())
                                    .build()
                    ))
                    .maxTokens(500)
                    .temperature(0.1)
                    .build();

            // 调用OpenAI API
            ChatCompletion response = openAIClient.chat().completions().create(request);

            // 处理响应
            return processResponse(response);

        } catch (Exception e) {
            log.error("使用OpenAI提取商品重量和尺寸时出错: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 构建提示词
     */
    private String buildPrompt(String title, String feature, String description) {
        StringBuilder prompt = new StringBuilder();
        prompt.append("请从以下商品信息中提取运输重量和包装尺寸信息：\n\n");

        if (StringUtils.hasText(title)) {
            prompt.append("商品标题：").append(title).append("\n\n");
        }

        if (StringUtils.hasText(feature)) {
            prompt.append("商品特性：").append(feature).append("\n\n");
        }

        if (StringUtils.hasText(description)) {
            prompt.append("商品描述：").append(description).append("\n\n");
        }

        prompt.append("【重要】单位转换要求：\n");
        prompt.append("1. 重量转换（必须转换为KG）：\n");
        prompt.append("   - 150g = 150 ÷ 1000 = 0.15 KG\n");
        prompt.append("   - 500g = 500 ÷ 1000 = 0.5 KG\n");
        prompt.append("   - 1200g = 1200 ÷ 1000 = 1.2 KG\n");
        prompt.append("   - 2.5lb = 2.5 × 0.453592 = 1.134 KG\n");
        prompt.append("   - 如果已经是KG，直接使用数值\n");
        prompt.append("2. 尺寸转换（必须转换为CM）：\n");
        prompt.append("   - 10mm = 10 ÷ 10 = 1 CM\n");
        prompt.append("   - 0.8cm = 0.8 CM（已经是CM）\n");
        prompt.append("   - 4inch = 4 × 2.54 = 10.16 CM\n");
        prompt.append("   - 0.2m = 0.2 × 100 = 20 CM\n");
        prompt.append("3. 【严格禁止】：\n");
        prompt.append("   - 禁止将150g转换为150KG（错误示例）\n");
        prompt.append("   - 禁止保留原始单位\n");
        prompt.append("   - 禁止不进行单位转换\n");
        prompt.append("4. 输出单位固定为：\n");
        prompt.append("   - shippingWeightUnitOfMeasure = \"KG\"\n");
        prompt.append("   - itemDepthUnit = \"CM\"\n");
        prompt.append("   - itemWidthUnit = \"CM\"\n");
        prompt.append("   - itemHeightUnitOfMeasure = \"CM\"\n");
        prompt.append("5. 如果信息不明确或缺失，对应字段设为null\n");
        prompt.append("6. 重量和尺寸必须是正数");

        return prompt.toString();
    }

    /**
     * 创建Function定义
     */
    private FunctionDefinition createFunctionDefinition() {
        // 构建JSON Schema
        Map<String, Object> schema = new HashMap<>();
        schema.put("type", "object");

        Map<String, Object> properties = new HashMap<>();

        // 运输重量
        Map<String, Object> shippingWeight = new HashMap<>();
        shippingWeight.put("type", "number");
        shippingWeight.put("description", "运输重量数值，必须转换为KG单位。重要：150g=0.15KG，500g=0.5KG，1200g=1.2KG。禁止将克数直接作为KG数值！");
        properties.put("shippingWeight", shippingWeight);

        Map<String, Object> shippingWeightUnit = new HashMap<>();
        shippingWeightUnit.put("type", "string");
        shippingWeightUnit.put("description", "运输重量单位，必须固定为\"KG\"");
        shippingWeightUnit.put("enum", Arrays.asList("KG"));
        properties.put("shippingWeightUnitOfMeasure", shippingWeightUnit);

        // 深度（长度）
        Map<String, Object> itemDepth = new HashMap<>();
        itemDepth.put("type", "number");
        itemDepth.put("description", "商品深度（前后）数值，必须转换为CM单位。如果原始单位是mm，需要除以10；如果是m，需要乘以100；如果是inch，需要乘以2.54");
        properties.put("itemDepthFrontToBack", itemDepth);

        Map<String, Object> itemDepthUnit = new HashMap<>();
        itemDepthUnit.put("type", "string");
        itemDepthUnit.put("description", "深度单位，必须固定为\"CM\"");
        itemDepthUnit.put("enum", Arrays.asList("CM"));
        properties.put("itemDepthUnit", itemDepthUnit);

        // 宽度
        Map<String, Object> itemWidth = new HashMap<>();
        itemWidth.put("type", "number");
        itemWidth.put("description", "商品宽度（左右）数值，必须转换为CM单位。如果原始单位是mm，需要除以10；如果是m，需要乘以100；如果是inch，需要乘以2.54");
        properties.put("itemWidthSideToSide", itemWidth);

        Map<String, Object> itemWidthUnit = new HashMap<>();
        itemWidthUnit.put("type", "string");
        itemWidthUnit.put("description", "宽度单位，必须固定为\"CM\"");
        itemWidthUnit.put("enum", Arrays.asList("CM"));
        properties.put("itemWidthUnit", itemWidthUnit);

        // 高度
        Map<String, Object> itemHeight = new HashMap<>();
        itemHeight.put("type", "number");
        itemHeight.put("description", "商品高度（底部到顶部）数值，必须转换为CM单位。如果原始单位是mm，需要除以10；如果是m，需要乘以100；如果是inch，需要乘以2.54");
        properties.put("itemHeightFloorToTop", itemHeight);

        Map<String, Object> itemHeightUnit = new HashMap<>();
        itemHeightUnit.put("type", "string");
        itemHeightUnit.put("description", "高度单位，必须固定为\"CM\"");
        itemHeightUnit.put("enum", Arrays.asList("CM"));
        properties.put("itemHeightUnitOfMeasure", itemHeightUnit);

        schema.put("properties", properties);

        try {
            String schemaJson = objectMapper.writeValueAsString(schema);
            return FunctionDefinition.builder()
                    .name("extract_product_dimensions_weight")
                    .description("从商品信息中提取运输重量（KG）和包装尺寸（CM）")
                    .parameters(FunctionParameters.builder()
                            .putAdditionalProperty("type", com.openai.core.JsonValue.from("object"))
                            .putAdditionalProperty("properties", com.openai.core.JsonValue.from(properties))
                            .build())
                    .build();
        } catch (JsonProcessingException e) {
            log.error("创建Function定义时出错: {}", e.getMessage(), e);
            throw new RuntimeException("创建Function定义失败", e);
        }
    }

    /**
     * 处理OpenAI响应
     */
    private ProductDimensionsAndWeight processResponse(ChatCompletion response) {
        try {
            List<ChatCompletion.Choice> choices = response.choices();
            if (choices == null || choices.isEmpty()) {
                log.warn("OpenAI响应为空");
                return null;
            }

            ChatCompletion.Choice choice = choices.get(0);
            ChatCompletionMessage message = choice.message();

            List<ChatCompletionMessageToolCall> toolCalls = message.toolCalls().orElse(null);
            if (toolCalls == null || toolCalls.isEmpty()) {
                log.warn("OpenAI响应中没有tool calls");
                return null;
            }

            ChatCompletionMessageToolCall toolCall = toolCalls.get(0);
            if (toolCall.function() == null) {
                log.warn("OpenAI响应中没有function call");
                return null;
            }

            String argumentsJson = toolCall.function().arguments();
            log.info("OpenAI返回的参数: {}", argumentsJson);

            // 解析JSON参数
            JsonNode argumentsNode = objectMapper.readTree(argumentsJson);

            ProductDimensionsAndWeight result = ProductDimensionsAndWeight.builder()
                    .shippingWeight(parseDecimal(argumentsNode, "shippingWeight"))
                    .shippingWeightUnitOfMeasure(parseString(argumentsNode, "shippingWeightUnitOfMeasure"))
                    .itemDepthFrontToBack(parseDecimal(argumentsNode, "itemDepthFrontToBack"))
                    .itemDepthUnit(parseString(argumentsNode, "itemDepthUnit"))
                    .itemWidthSideToSide(parseDecimal(argumentsNode, "itemWidthSideToSide"))
                    .itemWidthUnit(parseString(argumentsNode, "itemWidthUnit"))
                    .itemHeightFloorToTop(parseDecimal(argumentsNode, "itemHeightFloorToTop"))
                    .itemHeightUnitOfMeasure(parseString(argumentsNode, "itemHeightUnitOfMeasure"))
                    .build();

            log.info("成功提取商品重量和尺寸信息: {}", result);
            return result;

        } catch (JsonProcessingException e) {
            log.error("解析OpenAI响应JSON时出错: {}", e.getMessage(), e);
            return null;
        } catch (Exception e) {
            log.error("处理OpenAI响应时出错: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 从JSON节点中解析BigDecimal值
     */
    private BigDecimal parseDecimal(JsonNode node, String fieldName) {
        JsonNode fieldNode = node.get(fieldName);
        if (fieldNode == null || fieldNode.isNull()) {
            return null;
        }

        try {
            return new BigDecimal(fieldNode.asText());
        } catch (NumberFormatException e) {
            log.warn("无法解析字段 {} 的数值: {}", fieldName, fieldNode.asText());
            return null;
        }
    }

    /**
     * 从JSON节点中解析字符串值
     */
    private String parseString(JsonNode node, String fieldName) {
        JsonNode fieldNode = node.get(fieldName);
        if (fieldNode == null || fieldNode.isNull()) {
            return null;
        }

        String value = fieldNode.asText();
        return StringUtils.hasText(value) ? value : null;
    }
}
